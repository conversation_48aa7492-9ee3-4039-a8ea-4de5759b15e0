import request from './request' // 导入request函数

// 获取医疗检查报告列表
export const getMedicalCheckList = (params) => {
  return request({
    url: '/medical_check/query',
    method: 'post',
    data: params
  })
}

// 获取医疗检查详细信息
export const getMedicalCheckDetail = (medical_id) => {
  return request({
    url: '/medical_check_detail/query',
    method: 'post',
    data: {
      medical_id
    }
  })
}

// 根据条件筛选医疗检查详情
export const getMedicalCheckDetailByFilter = (params) => {
  return request({
    url: '/medical_detail_check/query',
    method: 'post',
    data: params
  })
}

// 获取医疗指标列表
export const getMedicalIndex = (params) => {
  return request({
    url: '/medical_index/query',
    method: 'post',
    data: params
  })
}

// 获取医疗指标列表
export const getMedicalIndexValue = (params) => {
  return request({
    url: '/medical_index_value/query',
    method: 'post',
    data: params
  })
}

// 获取医疗指标详情
export const getMedicalIndexDetail = (index_name) => {
  return request({
    url: '/medical_index_detail/query',
    method: 'post',
    data: {
      index_name
    }
  })
}

// 获取医疗指标类型列表
export const getMedicalTypes = () => {
  return request({
    url: '/medical_type/query',
    method: 'post',
    data: {}
  })
}

// 添加收藏指标
export const addFavoriteIndex = (params) => {
  return request({
    url: '/user_index',
    method: 'post',
    data: params
  })
}

// 取消收藏指标
export const removeFavoriteIndex = (params) => {
  return request({
    url: '/user_index',
    method: 'delete',
    data: params
  })
}

// 更新医疗检查备注
export const updateMedicalCheckComment = (medical_id, data) => {
  return request({
    url: `/medical_check/${medical_id}`,
    method: 'put',
    data: data
  })
}

// 添加医疗检查详情
export const addMedicalCheckDetail = (params) => {
  return request({
    url: '/medical_check_detail',
    method: 'post',
    data: params
  })
}

// 删除医疗检查数据
export const deleteMedicalCheckDetail = (medical_id) => {
  return request({
    url: `/medical_check/${medical_id}`,
    method: 'delete'
  })
}

// 获取检验报告列表
export const getMedicalExamList = (params) => {
  return request({
    url: '/medical_exam/query',
    method: 'post',
    data: params
  })
}

// 获取主页概览数据
export const getHomeOverview = () => {
  return request({
    url: '/home/<USER>',
    method: 'post',
    data: {}
  })
}

// 获取最新医疗检查报告
export const getMedicalExamLatest = () => {
  return request({
    url: '/medical_exam_latest/query',
    method: 'post',
    data: {
      exam_type: 8
    }
  })
}

// 获取病理报告列表
export const getPathologyReportList = (params) => {
  return request({
    url: '/pathology_report/query',
    method: 'post',
    data: params
  })
}

// 获取病理报告图片
export const getPathologyReportImages = (reportId) => {
  return request({
    url: '/pathology_report/images',
    method: 'post',
    data: {
      report_id: reportId
    }
  })
}

// 获取病情记录列表
export const getMedicalRecordList = (params) => {
  return request({
    url: '/medical_record/query',
    method: 'post',
    data: params
  })
}

// 添加病情记录
export const addMedicalRecord = (params) => {
  return request({
    url: '/medical_record',
    method: 'post',
    data: params
  })
}

// 更新病情记录
export const updateMedicalRecord = (recordId, params) => {
  return request({
    url: `/medical_record/${recordId}`,
    method: 'put',
    data: params
  })
}

// 删除病情记录
export const deleteMedicalRecord = (recordId) => {
  return request({
    url: `/medical_record/${recordId}`,
    method: 'delete'
  })
}
