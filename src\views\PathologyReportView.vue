<template>
  <div class="pathology-container">
    <!-- 动态背景元素 -->
    <div class="bg-animation">
      <div class="floating-cross" v-for="n in 8" :key="n" :style="{animationDelay: n * 0.6 + 's'}">+</div>
      <div class="floating-circle" v-for="n in 6" :key="n" :style="{animationDelay: n * 0.8 + 's'}"></div>
      <div class="floating-heart" v-for="n in 4" :key="n" :style="{animationDelay: n * 1.2 + 's'}">♥</div>
    </div>

    <back-button title="病理报告" />

    <!-- 报告列表 -->
    <div class="list-section">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多数据了"
          loading-text="加载中..."
          @load="onLoad"
        >
          <div 
            v-for="item in reportList" 
            :key="item.id" 
            class="report-item"
            @click="viewImages(item)"
          >
            <div class="report-header">
              <div class="report-title">{{ item.report_title || '病理报告' }}</div>
              <div class="report-date">{{ formatDate(item.report_date) }}</div>
            </div>
            <div class="report-info">
              <div class="info-item">
                <span class="label">医院：</span>
                <span class="value">{{ item.hospital || '未知医院' }}</span>
              </div>
            </div>
            <div class="report-hint">点击查看详情</div>
          </div>
        </van-list>
        
        <!-- 添加回到顶部按钮 -->
        <van-back-top />

      </van-pull-refresh>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-if="!loading && reportList.length === 0">
      <div class="empty-icon">🔬</div>
      <div class="empty-title">暂无病理报告</div>
      <div class="empty-subtitle">您还没有上传任何病理报告</div>
    </div>
  </div>
</template>

<script setup>
import BackButton from '@/components/index-detail/BackButton.vue';
import { ref, reactive, onMounted } from 'vue';
import { showToast, showImagePreview } from 'vant';
import { getPathologyReportList } from '@/api/medical';

// 响应式数据
const reportList = ref([]);
const loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);

// 分页参数
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
});

// 方法
const onLoad = async () => {
  try {
    loading.value = true;
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize
    }
    
    const response = await getPathologyReportList(params);
    
    if (response.data) {
      const newData = response.data.data || []; 
      
      pagination.total = response.data.pagination?.total || 0;
      
      if (refreshing.value) {
        reportList.value = newData;
        refreshing.value = false;
      } else {
        reportList.value = [...reportList.value, ...newData];
      }
      
      // 判断是否还有更多数据
      finished.value = pagination.page >= Math.ceil(pagination.total / pagination.pageSize);
      pagination.page++;
    }
  } catch (error) {
    console.error('获取病理报告列表失败:', error);
    showToast('网络错误，请稍后重试');
    finished.value = true;
  } finally {
    loading.value = false;
  }
};

const onRefresh = async () => {
  try {
    refreshing.value = true;
    pagination.page = 1;
    finished.value = false;
    reportList.value = [];
    await onLoad();
  } catch (error) {
    console.error('刷新失败:', error);
  } finally {
    refreshing.value = false;
  }
};

// 查看报告图片
const viewImages = (item) => {
  try {
    // 直接使用reportList中的image字段（base64数据）
    if (item.image && item.image.length > 0) {
      // 处理base64图片数据，确保格式正确
      const images = Array.isArray(item.image) ? item.image : [item.image];
      const processedImages = images.map(img => {
        // 如果base64数据没有data:image前缀，添加它
        if (img && !img.startsWith('data:image')) {
          return `data:image/jpeg;base64,${img}`;
        }
        return img;
      }).filter(img => img); // 过滤掉空值

      if (processedImages.length > 0) {
        // 使用 Vant 的 showImagePreview 显示图片
        showImagePreview({
          images: processedImages,
          startPosition: 0,
          closeable: true,
          showIndex: true,
          showIndicators: true
        });
      } else {
        showToast('暂无图片数据');
      }
    } else {
      showToast('暂无图片数据');
    }
  } catch (error) {
    console.error('显示报告图片失败:', error);
    showToast('显示图片失败');
  }
};

const formatDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
};

onMounted(() => {
  onLoad();
});
</script>

<style scoped>
.pathology-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
  overflow-x: hidden;
}

/* 动态背景元素 */
.bg-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-cross {
  position: absolute;
  color: rgba(255, 255, 255, 0.06);
  font-size: 18px;
  font-weight: bold;
  animation: float 8s ease-in-out infinite;
}

.floating-cross:nth-child(1) { top: 8%; left: 10%; }
.floating-cross:nth-child(2) { top: 20%; right: 15%; }
.floating-cross:nth-child(3) { top: 50%; left: 5%; }
.floating-cross:nth-child(4) { top: 70%; right: 10%; }
.floating-cross:nth-child(5) { top: 35%; left: 85%; }
.floating-cross:nth-child(6) { bottom: 30%; left: 20%; }
.floating-cross:nth-child(7) { bottom: 10%; right: 25%; }
.floating-cross:nth-child(8) { top: 85%; left: 65%; }

.floating-circle {
  position: absolute;
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.06);
  border-radius: 50%;
  animation: pulse 6s ease-in-out infinite;
}

.floating-circle:nth-child(9) { top: 15%; left: 55%; }
.floating-circle:nth-child(10) { top: 60%; right: 30%; }
.floating-circle:nth-child(11) { bottom: 20%; left: 70%; }
.floating-circle:nth-child(12) { top: 40%; right: 8%; }
.floating-circle:nth-child(13) { bottom: 40%; left: 40%; }
.floating-circle:nth-child(14) { top: 80%; right: 50%; }

.floating-heart {
  position: absolute;
  color: rgba(255, 255, 255, 0.04);
  font-size: 16px;
  animation: heartbeat 5s ease-in-out infinite;
}

.floating-heart:nth-child(15) { top: 25%; left: 25%; }
.floating-heart:nth-child(16) { top: 65%; right: 20%; }
.floating-heart:nth-child(17) { bottom: 15%; left: 75%; }
.floating-heart:nth-child(18) { top: 90%; left: 45%; }

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-12px) rotate(180deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.06; }
  50% { transform: scale(1.2); opacity: 0.12; }
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  25% { transform: scale(1.05); }
  50% { transform: scale(1); }
  75% { transform: scale(1.02); }
}

.list-section {
  position: relative;
  z-index: 2;
  height: calc(100vh - 50px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.report-item {
  background: rgba(255, 255, 255, 0.95);
  margin-bottom: 15px;
  padding: 15px;
  border-radius: 12px;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s ease;
}

.report-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.report-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c5aa0;
}

.report-date {
  font-size: 12px;
  color: #8b9dc3;
  background: rgba(44, 90, 160, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
}

.report-info {
  margin-bottom: 10px;
}

.info-item {
  display: flex;
  margin-bottom: 6px;
  font-size: 14px;
}

.label {
  color: #6b7280;
  width: 50px;
  flex-shrink: 0;
}

.value {
  color: #374151;
  flex: 1;
}

.report-hint {
  text-align: center;
  color: #6b7280;
  font-size: 12px;
  font-style: italic;
  margin-top: 8px;
  padding: 8px;
  background: rgba(44, 90, 160, 0.05);
  border-radius: 6px;
}

/* 空状态 */
.empty-state {
  position: relative;
  z-index: 2;
  text-align: center;
  padding: 60px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  margin-top: 50px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-title {
  color: #374151;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.empty-subtitle {
  color: #6b7280;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .pathology-container {
    padding: 10px;
  }
  
  .report-item {
    padding: 12px;
  }
}
</style>
