<template>
  <div class="medical-record-container">
    <!-- 动态背景元素 -->
    <div class="bg-animation">
      <div class="floating-cross" v-for="n in 8" :key="n" :style="{animationDelay: n * 0.6 + 's'}">+</div>
      <div class="floating-circle" v-for="n in 6" :key="n" :style="{animationDelay: n * 0.8 + 's'}"></div>
      <div class="floating-heart" v-for="n in 4" :key="n" :style="{animationDelay: n * 1.2 + 's'}">♥</div>
    </div>

    <back-button title="病情记录" />

    <!-- 报告开关区域 -->
    <div class="switch-section">
      <div class="switch-card">
        <div class="switch-item">
          <span class="switch-label">显示报告</span>
          <van-switch v-model="showReports" size="22px" @change="onSwitchChange" />
        </div>
        <div class="switch-hint">开启后将在记录中显示相关报告</div>
      </div>
    </div>

    <!-- 折叠面板区域 -->
    <div class="collapse-section">
      <van-collapse v-model="activeNames" accordion>
        <van-collapse-item
          v-for="(group, index) in groupedRecords"
          :key="group.key"
          :name="group.key"
          :title="group.title"
        >
          <!-- 混合显示病情记录和报告项 -->
          <div
            v-for="item in group.items"
            :key="item.key"
            :class="item.type === 'record' ? 'record-item' : 'report-item'"
            @click="item.type === 'report' ? navigateToReport(item) : null"
          >
            <div class="item-header">
              <div class="item-title">{{ item.title }}</div>
              <div class="item-date">{{ item.displayDate || item.date }}</div>
            </div>

            <!-- 病情记录内容 -->
            <div v-if="item.type === 'record'" class="record-content">
              <div class="record-description">{{ item.description }}</div>
              <div class="record-footer">
                <div class="record-status" :class="getStatusClass(item.status)">
                  {{ item.status }}
                </div>
                <div class="record-actions">
                  <van-button size="mini" type="primary" @click="handleEdit(item)">编辑</van-button>
                  <van-button size="mini" type="danger" @click="handleDelete(item)">删除</van-button>
                </div>
              </div>
            </div>

            <!-- 报告内容 -->
            <div v-else class="report-content">
              <div class="report-type">{{ item.reportType }}</div>
              <div class="report-hint">点击查看详情</div>
            </div>
          </div>
        </van-collapse-item>
      </van-collapse>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-if="groupedRecords.length === 0">
      <div class="empty-icon">📝</div>
      <div class="empty-title">暂无病情记录</div>
      <div class="empty-subtitle">您还没有任何病情记录</div>
    </div>

    <!-- 添加按钮 -->
    <van-floating-bubble
      axis="xy"
      icon="plus"
      @click="handleAdd"
      :style="{ right: '20px', bottom: '80px' }"
    />

    <!-- 编辑弹窗 -->
    <van-dialog
      v-model:show="showEditDialog"
      :title="editMode === 'add' ? '添加病情记录' : '编辑病情记录'"
      show-cancel-button
      @confirm="handleSave"
      @cancel="handleCancel"
      :confirm-button-loading="saving"
    >
      <div class="edit-form">
        <van-field
          v-model="editForm.title"
          label="标题"
          placeholder="请输入记录标题"
          required
        />
        <van-field
          v-model="editForm.date"
          label="日期"
          placeholder="请选择日期"
          readonly
          clickable
          @click="showDatePicker = true"
          required
        />
        <van-field
          v-model="editForm.description"
          label="描述"
          type="textarea"
          placeholder="请输入详细描述"
          rows="4"
          required
        />
        <van-field
          v-model="editForm.status"
          label="状态"
          placeholder="请输入状态"
          required
        />
      </div>
    </van-dialog>

    <!-- 日期选择器 -->
    <van-date-picker
      v-model="currentDate"
      :show="showDatePicker"
      @confirm="onDateConfirm"
      @cancel="showDatePicker = false"
      title="选择日期"
    />
  </div>
</template>

<script setup>
import BackButton from '@/components/index-detail/BackButton.vue';
import { ref, computed, onMounted, onUnmounted, onActivated, onDeactivated, watch, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { showToast, showConfirmDialog } from 'vant';
import { getMedicalCheckList, getMedicalExamList, getMedicalRecordList, addMedicalRecord, updateMedicalRecord, deleteMedicalRecord } from '@/api/medical';

const router = useRouter();

// 响应式数据
const showReports = ref(false);
const reportList = ref([]);
const examList = ref([]);
const loading = ref(false);
const activeNames = ref('');

// 编辑相关数据
const showEditDialog = ref(false);
const showDatePicker = ref(false);
const editMode = ref('add'); // 'add' 或 'edit'
const saving = ref(false);
const currentDate = ref([new Date().getFullYear(), new Date().getMonth(), new Date().getDate()]);
const editForm = ref({
  id: null,
  title: '',
  date: '',
  description: '',
  status: ''
});

// 真实的病情记录数据（从后端获取）
const realRecords = ref([]);

// 模拟病情记录数据
const mockRecords = ref([
  {
    id: 1,
    title: '第1次化疗',
    date: '2025-04-25',
    description: '吉西他滨1540mg 紫杉醇190mg 状态良好，肚子有点疼',
    status: '状态良好',
    month: '2025-04'
  },
  {
    id: 2,
    title: '第2次化疗',
    date: '2025-05-15',
    description: '吉西他滨1540mg 紫杉醇190mg 状态一般，有轻微恶心',
    status: '状态一般',
    month: '2025-05'
  },
  {
    id: 3,
    title: '第3次化疗',
    date: '2025-06-05',
    description: '吉西他滨1540mg 紫杉醇190mg 状态良好，食欲正常',
    status: '状态良好',
    month: '2025-06'
  }
]);

// 分组数据
const groupedRecords = computed(() => {
  try {
    const groups = {};

    // 按月份分组病情记录
    if (mockRecords.value && Array.isArray(mockRecords.value)) {
      mockRecords.value.forEach(record => {
        const monthKey = record.month;
        if (!groups[monthKey]) {
          groups[monthKey] = {
            key: monthKey,
            title: record.month.substring(0, 4) + '年' + record.month.substring(5, 7) + '月',
            items: []
          };
        }
        groups[monthKey].items.push({
          key: `record-${record.id}`,
          type: 'record',
          id: record.id,
          title: record.title,
          date: record.date, // 保持原始日期格式用于排序
          displayDate: formatDate(record.date), // 用于显示的格式化日期
          description: record.description,
          status: record.status
        });
      });
    }

    // 如果开启报告显示，添加报告数据
    if (showReports.value) {
      // 处理检验报告
      if (reportList.value && Array.isArray(reportList.value)) {
        reportList.value.forEach(report => {
          const reportDate = new Date(report.medical_date);
          const monthKey = `${reportDate.getFullYear()}-${String(reportDate.getMonth() + 1).padStart(2, '0')}`;
          if (!groups[monthKey]) {
            groups[monthKey] = {
              key: report.medical_id,
              title: `${reportDate.getFullYear()}年${reportDate.getMonth() + 1}月`,
              items: []
            };
          }
          groups[monthKey].items.push({
            key: `report-${report.medical_id}`,
            type: 'report',
            id: report.id,
            title: report.medical_type || '检验报告',
            date: report.medical_date, // 保持原始日期格式用于排序
            displayDate: formatDate(report.medical_date), // 用于显示的格式化日期
            reportType: '检验报告',
            routeName: 'reportView',
            data: report
          });
        });
      }

      // 处理检查报告
      if (examList.value && Array.isArray(examList.value)) {
        examList.value.forEach(exam => {
      const examDate = new Date(exam.medical_date);
      const monthKey = `${examDate.getFullYear()}-${String(examDate.getMonth() + 1).padStart(2, '0')}`;
      if (!groups[monthKey]) {
        groups[monthKey] = {
          key: exam.exam_id,
          title: `${examDate.getFullYear()}年${examDate.getMonth() + 1}月`,
          items: []
        };
      }
      groups[monthKey].items.push({
        key: `exam-${exam.exam_id}`,
        type: 'report',
        id: exam.id,
        title: exam.exam_type || '检查报告',
        date: exam.medical_date, // 保持原始日期格式用于排序
        displayDate: formatDate(exam.medical_date), // 用于显示的格式化日期
        reportType: '检查报告',
        routeName: 'examReportView',
        data: exam
        });
      });
    }
  }

    // 对每个组内的项目按日期排序，并对组按时间倒序排列
    const sortedGroups = Object.values(groups).map(group => ({
      ...group,
      items: group.items.sort((a, b) => {
        const dateA = new Date(a.date);
        const dateB = new Date(b.date);
        return dateB - dateA; // 倒序排列，最新的在前
      })
    })).sort((a, b) => b.key.localeCompare(a.key));

    return sortedGroups || [];
  } catch (error) {
    console.error('Error in groupedRecords computed:', error);
    return [];
  }
});

// 方法
const onSwitchChange = async (value) => {
  if (value) {
    await loadReports();
  }
};

const loadReports = async () => {
  try {
    loading.value = true;

    // 获取检验报告
    const reportResponse = await getMedicalCheckList({ page: 1, pageSize: 50 });
    if (reportResponse.data) {
      reportList.value = reportResponse.data.data || [];
    }

    // 获取检查报告
    const examResponse = await getMedicalExamList({ page: 1, pageSize: 50 });
    if (examResponse.data) {
      examList.value = examResponse.data.data || [];
    }
  } catch (error) {
    console.error('获取报告数据失败:', error);
    showToast('获取报告数据失败');
  } finally {
    loading.value = false;
  }
};

const navigateToReport = (report) => {
  if (report.routeName === 'reportView') {
    // 跳转到检验报告并显示详情
    router.push({
      name: report.routeName,
      query: {
        showDetail: 'true',  // 确保是字符串
        medicalId: String(report.data.medical_id)  // 确保是字符串
      }
    });
  } else {
    router.push({ name: report.routeName });
  }
};

// 设置默认展开最新时间
const setDefaultActivePanel = () => {
  try {
    if (groupedRecords.value && groupedRecords.value.length > 0 && groupedRecords.value[0].key) {
      activeNames.value = groupedRecords.value[0].key;
    }
  } catch (error) {
    console.error('Error setting default active panel:', error);
  }
};

const getStatusClass = (status) => {
  return {
    'status-good': status.includes('良好'),
    'status-normal': status.includes('一般'),
    'status-poor': status.includes('不佳')
  };
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    return `${date.getMonth() + 1}月${date.getDate()}日`;
  } catch (error) {
    console.error('Date formatting error:', error);
    return dateString;
  }
};

// 病情记录CRUD方法
const loadMedicalRecords = async () => {
  try {
    loading.value = true;
    const response = await getMedicalRecordList({ page: 1, pageSize: 100 });
    if (response.data && response.data.data) {
      realRecords.value = response.data.data;
      // 将真实数据合并到模拟数据中
      mockRecords.value = [...mockRecords.value, ...realRecords.value.map(record => ({
        id: record.id,
        title: record.title,
        date: record.record_date,
        description: record.description,
        status: record.status,
        month: record.record_date.substring(0, 7)
      }))];
    }
  } catch (error) {
    console.error('获取病情记录失败:', error);
    showToast('获取病情记录失败');
  } finally {
    loading.value = false;
  }
};

const handleAdd = () => {
  editMode.value = 'add';
  editForm.value = {
    id: null,
    title: '',
    date: '',
    description: '',
    status: ''
  };
  showEditDialog.value = true;
};

const handleEdit = (item) => {
  editMode.value = 'edit';
  editForm.value = {
    id: item.id,
    title: item.title,
    date: item.date,
    description: item.description,
    status: item.status
  };
  showEditDialog.value = true;
};

const handleDelete = async (item) => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '确定要删除这条病情记录吗？'
    });

    loading.value = true;
    await deleteMedicalRecord(item.id);
    showToast('删除成功');

    // 从本地数据中移除
    const index = mockRecords.value.findIndex(record => record.id === item.id);
    if (index > -1) {
      mockRecords.value.splice(index, 1);
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error);
      showToast('删除失败');
    }
  } finally {
    loading.value = false;
  }
};

const handleSave = async () => {
  if (!editForm.value.title || !editForm.value.date || !editForm.value.description || !editForm.value.status) {
    showToast('请填写完整信息');
    return;
  }

  try {
    saving.value = true;

    const recordData = {
      title: editForm.value.title,
      record_date: editForm.value.date,
      description: editForm.value.description,
      status: editForm.value.status
    };

    if (editMode.value === 'add') {
      const response = await addMedicalRecord(recordData);
      if (response.data) {
        showToast('添加成功');
        // 添加到本地数据
        const newRecord = {
          id: response.data.id || Date.now(),
          title: editForm.value.title,
          date: editForm.value.date,
          description: editForm.value.description,
          status: editForm.value.status,
          month: editForm.value.date.substring(0, 7)
        };
        mockRecords.value.push(newRecord);
      }
    } else {
      await updateMedicalRecord(editForm.value.id, recordData);
      showToast('修改成功');
      // 更新本地数据
      const index = mockRecords.value.findIndex(record => record.id === editForm.value.id);
      if (index > -1) {
        mockRecords.value[index] = {
          ...mockRecords.value[index],
          title: editForm.value.title,
          date: editForm.value.date,
          description: editForm.value.description,
          status: editForm.value.status,
          month: editForm.value.date.substring(0, 7)
        };
      }
    }

    showEditDialog.value = false;
  } catch (error) {
    console.error('保存失败:', error);
    showToast('保存失败');
  } finally {
    saving.value = false;
  }
};

const handleCancel = () => {
  showEditDialog.value = false;
  editForm.value = {
    id: null,
    title: '',
    date: '',
    description: '',
    status: ''
  };
};

const onDateConfirm = (value) => {
  // value 是一个数组 [year, month, day]
  if (Array.isArray(value) && value.length >= 3) {
    const [year, month, day] = value;
    editForm.value.date = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
  }
  showDatePicker.value = false;
};

// 监听分组数据变化，设置默认展开
let watchStopHandle = null;

onMounted(async () => {
  // 加载病情记录数据
  await loadMedicalRecords();

  // 页面加载时设置默认展开最新时间
  setDefaultActivePanel();

  // 设置监听器
  watchStopHandle = watch(groupedRecords, (newVal) => {
    if (newVal && newVal.length > 0) {
      nextTick(() => {
        setDefaultActivePanel();
      });
    }
  }, { immediate: false });
});

// keepAlive激活时
onActivated(() => {
  // 重新设置默认展开面板
  nextTick(() => {
    setDefaultActivePanel();
  });
});

// keepAlive失活时
onDeactivated(() => {
  // 可以在这里保存一些状态，但不清理数据
});

onUnmounted(() => {
  // 清理监听器
  if (watchStopHandle) {
    watchStopHandle();
    watchStopHandle = null;
  }

  // 清理数据
  activeNames.value = '';
  reportList.value = [];
  examList.value = [];
});
</script>

<style scoped>
.medical-record-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
  overflow-x: hidden;
}

/* 动态背景元素 */
.bg-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-cross {
  position: absolute;
  color: rgba(255, 255, 255, 0.06);
  font-size: 18px;
  font-weight: bold;
  animation: float 8s ease-in-out infinite;
}

.floating-circle {
  position: absolute;
  width: 12px;
  height: 12px;
  border: 2px solid rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  animation: float 10s ease-in-out infinite reverse;
}

.floating-heart {
  position: absolute;
  color: rgba(255, 255, 255, 0.04);
  font-size: 16px;
  animation: float 12s ease-in-out infinite;
}

/* 动画定义 */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-20px) rotate(90deg); }
  50% { transform: translateY(-10px) rotate(180deg); }
  75% { transform: translateY(-30px) rotate(270deg); }
}

/* 随机位置 */
.floating-cross:nth-child(1) { top: 10%; left: 10%; animation-duration: 8s; }
.floating-cross:nth-child(2) { top: 20%; right: 15%; animation-duration: 9s; }
.floating-cross:nth-child(3) { top: 60%; left: 20%; animation-duration: 7s; }
.floating-cross:nth-child(4) { top: 80%; right: 25%; animation-duration: 10s; }
.floating-cross:nth-child(5) { top: 30%; left: 70%; animation-duration: 8.5s; }
.floating-cross:nth-child(6) { top: 50%; right: 10%; animation-duration: 9.5s; }
.floating-cross:nth-child(7) { top: 70%; left: 60%; animation-duration: 7.5s; }
.floating-cross:nth-child(8) { top: 40%; right: 40%; animation-duration: 8.8s; }

.floating-circle:nth-child(9) { top: 15%; left: 30%; animation-duration: 10s; }
.floating-circle:nth-child(10) { top: 35%; right: 20%; animation-duration: 11s; }
.floating-circle:nth-child(11) { top: 55%; left: 80%; animation-duration: 9s; }
.floating-circle:nth-child(12) { top: 75%; right: 60%; animation-duration: 12s; }
.floating-circle:nth-child(13) { top: 25%; left: 50%; animation-duration: 10.5s; }
.floating-circle:nth-child(14) { top: 65%; right: 30%; animation-duration: 11.5s; }

.floating-heart:nth-child(15) { top: 45%; left: 15%; animation-duration: 12s; }
.floating-heart:nth-child(16) { top: 25%; right: 50%; animation-duration: 13s; }
.floating-heart:nth-child(17) { top: 85%; left: 40%; animation-duration: 11s; }
.floating-heart:nth-child(18) { top: 5%; right: 70%; animation-duration: 14s; }

/* 开关区域 */
.switch-section {
  position: relative;
  z-index: 2;
  margin-bottom: 20px;
}

.switch-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 15px 20px;
  border-radius: 12px;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.switch-label {
  font-size: 16px;
  font-weight: 500;
  color: #2c5aa0;
}

.switch-hint {
  font-size: 12px;
  color: #6b7280;
}

/* 折叠面板区域 */
.collapse-section {
  position: relative;
  z-index: 2;
  height: calc(100vh - 200px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 通用项目样式 */
.record-item, .report-item {
  background: rgba(255, 255, 255, 0.95);
  margin-bottom: 12px;
  padding: 15px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.record-item {
  border-left: 4px solid #4CAF50;
}

.report-item {
  border-left: 4px solid #2196F3;
  cursor: pointer;
}

.report-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.item-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c5aa0;
}

.item-date {
  font-size: 12px;
  color: #6b7280;
}

.record-content {
  margin-top: 8px;
}

.record-description {
  font-size: 14px;
  color: #374151;
  line-height: 1.5;
  margin-bottom: 8px;
}

.record-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-status {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.record-actions {
  display: flex;
  gap: 8px;
}

.status-good {
  background: #dcfce7;
  color: #16a34a;
}

.status-normal {
  background: #fef3c7;
  color: #d97706;
}

.status-poor {
  background: #fee2e2;
  color: #dc2626;
}

/* 报告内容样式 */
.report-content {
  margin-top: 8px;
}

.report-type {
  font-size: 12px;
  color: #2196F3;
  margin-bottom: 4px;
}

.report-hint {
  font-size: 11px;
  color: #9ca3af;
}

/* 空状态 */
.empty-state {
  position: relative;
  z-index: 2;
  text-align: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.8);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.empty-subtitle {
  font-size: 14px;
  opacity: 0.7;
}

/* 折叠面板样式覆盖 */
:deep(.van-collapse-item__title) {
  background: rgba(255, 255, 255, 0.95);
  color: #2c5aa0;
  font-weight: 600;
  font-size: 16px;
}

:deep(.van-collapse-item__wrapper) {
  background: transparent;
}

:deep(.van-collapse-item__content) {
  padding: 0;
  background: transparent;
}

:deep(.van-collapse-item) {
  background: rgba(255, 255, 255, 0.95);
  margin-bottom: 15px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

/* 编辑表单样式 */
.edit-form {
  padding: 20px 0;
}

.edit-form .van-field {
  margin-bottom: 16px;
}

/* 浮动按钮样式覆盖 */
:deep(.van-floating-bubble) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

:deep(.van-floating-bubble:hover) {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.6);
}
</style>
